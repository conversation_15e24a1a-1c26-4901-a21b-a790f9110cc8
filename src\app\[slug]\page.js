import React from 'react'
import ChatInterface from '../../components/ChatInterface'
import { generateDynamicMetadata } from '../../utils/metadata'

// Dynamic metadata generation using your ChatContext API logic
export async function generateMetadata({ params }) {
  const { slug } = await params

  // Use the utility function that integrates with your ChatContext API
  return await generateDynamicMetadata(slug)
}

const page = async ({ params, searchParams  }) => {
  const { slug } = await params
  const queryParams = await searchParams

  return (
    <ChatInterface slug={slug} query={queryParams} />
  )
}

export default page