{"name": "driply", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"axios": "^1.11.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0"}, "devDependencies": {"@opennextjs/cloudflare": "^1.5.3", "@tailwindcss/postcss": "^4", "@types/node": "24.0.14", "@types/react": "19.1.8", "tailwindcss": "^4", "typescript": "5.8.3", "wrangler": "^4.25.1"}}