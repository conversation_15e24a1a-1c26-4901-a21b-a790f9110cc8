"use client";
import React from 'react';
import { useChatContext } from '../../contexts/ChatContext';

const TestContextPage = () => {
  const {
    metadata,
    updateMetadata,
    clearMetadata,
    getTitle,
    getKeywords,
    getDescription,
    getImage,
    hasMetadata,
    loading,
    error
  } = useChatContext();

  const handleTestMetadata = () => {
    updateMetadata({
      title: "test businessName GPT Chat",
      keywords: "test keywords",
      description: "This is a test description for the chat",
      image: ""
    });
  };

  const handleClearMetadata = () => {
    clearMetadata();
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8 text-center">Chat Context API Test</h1>
      
      <div className="space-y-6">
        <div className="flex gap-4 justify-center">
          <button
            onClick={handleTestMetadata}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Set Test Metadata
          </button>
          <button
            onClick={handleClearMetadata}
            className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            Clear Metadata
          </button>
        </div>

        {/* Inline Metadata Display */}
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-2">Chat Metadata</h3>
          {loading && <p className="text-blue-600">Loading metadata...</p>}
          {error && <p className="text-red-600">Error: {error}</p>}
          {!hasMetadata() && !loading && !error && (
            <p className="text-gray-600">No metadata available</p>
          )}
          {hasMetadata() && (
            <div className="space-y-2">
              {getTitle() && (
                <div>
                  <span className="font-medium text-green-700">Title:</span>
                  <span className="ml-2 text-green-600">{getTitle()}</span>
                </div>
              )}
              {getKeywords() && (
                <div>
                  <span className="font-medium text-green-700">Keywords:</span>
                  <span className="ml-2 text-green-600">{getKeywords()}</span>
                </div>
              )}
              {getDescription() && (
                <div>
                  <span className="font-medium text-green-700">Description:</span>
                  <span className="ml-2 text-green-600">{getDescription()}</span>
                </div>
              )}
              {getImage() && (
                <div>
                  <span className="font-medium text-green-700">Image:</span>
                  <span className="ml-2 text-green-600">{getImage()}</span>
                </div>
              )}

              {/* Display full metadata object for debugging */}
              <div className="mt-4 pt-4 border-t border-green-200">
                <span className="font-medium text-green-700">Full Metadata:</span>
                <pre className="mt-2 text-sm text-green-600 bg-green-100 p-2 rounded overflow-x-auto">
                  {JSON.stringify(metadata, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">How to Use the Context API</h2>
          <div className="space-y-2 text-sm">
            <p><strong>1.</strong> The context is automatically populated when a user visits a chat page (e.g., /username)</p>
            <p><strong>2.</strong> The API call to <code className="bg-gray-200 px-1 rounded">https://api-develop.driply.me/api/chat/settings?customerName=username</code> stores metadata</p>
            <p><strong>3.</strong> Any component can access this metadata using the <code className="bg-gray-200 px-1 rounded">useChatContext</code> hook</p>
            <p><strong>4.</strong> Use the buttons above to test setting and clearing metadata manually</p>
          </div>
        </div>

        <div className="bg-blue-50 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Code Example</h2>
          <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-x-auto">
{`import { useChatContext } from '../contexts/ChatContext';

const MyComponent = () => {
  const {
    metadata,
    getTitle,
    getKeywords,
    getDescription,
    getImage,
    hasMetadata
  } = useChatContext();

  return (
    <div>
      {hasMetadata() && (
        <div>
          <p>Title: {getTitle()}</p>
          <p>Keywords: {getKeywords()}</p>
          <p>Description: {getDescription()}</p>
          <p>Image: {getImage()}</p>
          <p>Full metadata: {JSON.stringify(metadata)}</p>
        </div>
      )}
    </div>
  );
};`}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TestContextPage;
