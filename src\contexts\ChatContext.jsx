"use client";
import React, { createContext, useContext, useState, useCallback } from 'react';

// Create the context
const ChatContext = createContext();

// Custom hook to use the chat context
export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

// Chat Provider component
export const ChatProvider = ({ children }) => {
  console.log("ChatProvider",  children);
  
  const [metadata, setMetadata] = useState({
    title: null,
    keywords: null,
    description: null,
    image: null,
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to update metadata
  const updateMetadata = useCallback((newMetadata) => {
    setMetadata(prevMetadata => ({
      ...prevMetadata,
      ...newMetadata
    }));
  }, []);

  // Function to clear metadata
  const clearMetadata = useCallback(() => {
    setMetadata({
      title: null,
      keywords: null,
      description: null,
      image: null,
    });
  }, []);

  // Function to set loading state
  const setLoadingState = useCallback((isLoading) => {
    setLoading(isLoading);
  }, []);

  // Function to set error state
  const setErrorState = useCallback((errorMessage) => {
    setError(errorMessage);
  }, []);

  // Function to clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value = {
    metadata,
    loading,
    error,
    updateMetadata,
    clearMetadata,
    setLoadingState,
    setErrorState,
    clearError,
    // Helper getters for easy access
    getTitle: () => metadata.title,
    getKeywords: () => metadata.keywords,
    getDescription: () => metadata.description,
    getImage: () => metadata.image,
    hasMetadata: () => metadata.title !== null || metadata.keywords !== null || metadata.description !== null,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

export default ChatContext;
