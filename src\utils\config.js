export const EXTERNAL_API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api-develop.driply.me/';

export const getExternalApiUrl = () => {
  const url = EXTERNAL_API_BASE_URL;
  return url.endsWith('/') ? url : `${url}/`;
};

export const API_ENDPOINTS = {
  CHAT_INIT: '/api/chat/init',
  CHAT_SEND: '/api/chat/send',
  CHAT_MESSAGES: '/api/chat/messages',
  CHAT_SETTINGS: '/api/chat/settings'
};

export const getSSEUrl = (userId) => {
  return `${getExternalApiUrl()}chat-pusher/chat?stream=${userId}`;
};

export const EXTERNAL_API_ENDPOINTS = {
  CHAT_INIT: `${getExternalApiUrl()}api/chat/init`,
  CHAT_SEND: `${getExternalApiUrl()}api/chat/message`,
  CHAT_MESSAGES: `${getExternalApiUrl()}api/chat/messages`,
  CHAT_SETTINGS: `${getExternalApiUrl()}api/chat/settings`
};
