import { NextResponse } from 'next/server';
import { EXTERNAL_API_ENDPOINTS } from '../../../../utils/config';

export async function POST(request) {
  try {
    const body = await request.json();

    const response = await fetch(EXTERNAL_API_ENDPOINTS.CHAT_INIT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to initialize chat' },
      { status: 500 }
    );
  }
}
