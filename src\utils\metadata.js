/**
 * Utility functions for handling dynamic metadata
 * This integrates with your ChatContext API logic for consistent metadata handling
 */

/**
 * Fetches metadata for a given customer/slug from your API
 * This mirrors the same API call used in ChatContext
 * @param {string} customerName - The slug/customer name
 * @returns {Promise<Object>} - Metadata object
 */
export async function fetchCustomerMetadata(customerName) {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-develop.driply.me'
  const apiUrl = `${baseUrl}/api/chat/settings?customerName=${customerName}`
  
  try {
    console.log('Fetching customer metadata for:', customerName)
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Cache for 5 minutes to improve performance
      next: { revalidate: 300 }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    console.log('Fetched customer data:', data)
    
    return data
  } catch (error) {
    console.error('Error fetching customer metadata:', error)
    return null
  }
}

/**
 * Generates Next.js metadata object from customer data
 * @param {string} slug - The customer slug
 * @param {Object} customerData - Data from your API
 * @returns {Object} - Next.js metadata object
 */
export function generateMetadataFromCustomerData(slug, customerData = null) {
  // Default fallback values
  let title = `Driply Chat - ${slug.charAt(0).toUpperCase() + slug.slice(1)}`
  let description = `Chat interface for ${slug} - Powered by Driply`
  let keywords = [`chat`, `${slug}`, `driply`, `ai`, `conversation`]
  let businessName = 'Driply'
  let image = '/og-image.jpg' // Default image
  
  // If we have customer data, use it to enhance metadata
  if (customerData && customerData.customerName) {
    businessName = customerData.businessName || 'Driply'
    const metaData = customerData.metaData || {}
    
    // Use metadata from your API response
    title = metaData.title || `${businessName} Chat - ${slug}`
    description = metaData.description || `Chat with ${businessName} - Powered by Driply`
    
    // Handle keywords
    if (metaData.keywords) {
      keywords = Array.isArray(metaData.keywords) 
        ? metaData.keywords 
        : metaData.keywords.split(',').map(k => k.trim())
    } else {
      keywords = [`chat`, `${slug}`, `${businessName.toLowerCase()}`, `driply`, `ai`, `conversation`]
    }
    
    // Use custom image if provided
    if (metaData.image) {
      image = metaData.image
    }
  }
  
  // Return complete Next.js metadata object
  return {
    title: title,
    description: description,
    keywords: keywords,
    openGraph: {
      title: title,
      description: description,
      url: `https://yourdomain.com/${slug}`, // Replace with your actual domain
      siteName: businessName,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: `${businessName} Chat - ${slug}`,
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [image],
    },
    robots: {
      index: true,
      follow: true,
    },
    // Additional metadata
    authors: [{ name: businessName }],
    creator: businessName,
    publisher: 'Driply',
  }
}

/**
 * Complete function that fetches data and generates metadata
 * Use this in your generateMetadata function in page.js
 * @param {string} slug - The customer slug
 * @returns {Promise<Object>} - Next.js metadata object
 */
export async function generateDynamicMetadata(slug) {
  const customerData = await fetchCustomerMetadata(slug)
  return generateMetadataFromCustomerData(slug, customerData)
}
